<?php
/*
* ----------------------------------------------------
* @author: DooPlay Mobile API
* <AUTHOR> https://doothemes.com/
* @copyright: (c) 2024 Mobile App API Extension
* ----------------------------------------------------
*
* @since 1.0.0
*
*/

if(!defined('ABSPATH')) die;

class DooMobileAPI {

    /**
     * Constructor
     * @since 1.0.0
     */
    public function __construct() {
        add_action('rest_api_init', array($this, 'register_api_routes'));
        add_action('init', array($this, 'add_cors_headers'));
    }

    /**
     * Add CORS headers for mobile app
     * @since 1.0.0
     */
    public function add_cors_headers() {
        add_action('rest_api_init', function() {
            remove_filter('rest_pre_serve_request', 'rest_send_cors_headers');
            add_filter('rest_pre_serve_request', function($value) {
                header('Access-Control-Allow-Origin: *');
                header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
                header('Access-Control-Allow-Headers: Content-Type, Authorization');
                return $value;
            });
        }, 15);
    }

    /**
     * Register all API routes
     * @since 1.0.0
     */
    public function register_api_routes() {
        
        // Movies endpoints
        register_rest_route('doomobile/v1', '/movies', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_movies'),
            'permission_callback' => '__return_true',
            'args' => array(
                'page' => array(
                    'default' => 1,
                    'sanitize_callback' => 'absint',
                ),
                'per_page' => array(
                    'default' => 20,
                    'sanitize_callback' => 'absint',
                ),
                'genre' => array(
                    'default' => '',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'year' => array(
                    'default' => '',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
            ),
        ));

        // TV Shows endpoints
        register_rest_route('doomobile/v1', '/tvshows', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_tvshows'),
            'permission_callback' => '__return_true',
            'args' => array(
                'page' => array(
                    'default' => 1,
                    'sanitize_callback' => 'absint',
                ),
                'per_page' => array(
                    'default' => 20,
                    'sanitize_callback' => 'absint',
                ),
            ),
        ));

        // Single movie/show details
        register_rest_route('doomobile/v1', '/content/(?P<id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_content_details'),
            'permission_callback' => '__return_true',
        ));

        // Video sources for content
        register_rest_route('doomobile/v1', '/video-sources/(?P<id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_video_sources'),
            'permission_callback' => '__return_true',
        ));

        // Search endpoint
        register_rest_route('doomobile/v1', '/search', array(
            'methods' => 'GET',
            'callback' => array($this, 'search_content'),
            'permission_callback' => '__return_true',
            'args' => array(
                'q' => array(
                    'required' => true,
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'type' => array(
                    'default' => 'all',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
            ),
        ));

        // Categories/Genres
        register_rest_route('doomobile/v1', '/categories', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_categories'),
            'permission_callback' => '__return_true',
        ));

        // Featured/Latest content
        register_rest_route('doomobile/v1', '/featured', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_featured_content'),
            'permission_callback' => '__return_true',
        ));

        // Episodes for TV shows
        register_rest_route('doomobile/v1', '/episodes/(?P<show_id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_episodes'),
            'permission_callback' => '__return_true',
            'args' => array(
                'season' => array(
                    'default' => 1,
                    'sanitize_callback' => 'absint',
                ),
            ),
        ));
    }

    /**
     * Get movies list
     * @since 1.0.0
     */
    public function get_movies($request) {
        $page = $request->get_param('page');
        $per_page = $request->get_param('per_page');
        $genre = $request->get_param('genre');
        $year = $request->get_param('year');

        $args = array(
            'post_type' => 'movies',
            'post_status' => 'publish',
            'posts_per_page' => $per_page,
            'paged' => $page,
            'meta_query' => array(),
        );

        // Add genre filter
        if (!empty($genre)) {
            $args['tax_query'] = array(
                array(
                    'taxonomy' => 'genres',
                    'field' => 'slug',
                    'terms' => $genre,
                ),
            );
        }

        // Add year filter
        if (!empty($year)) {
            $args['meta_query'][] = array(
                'key' => 'release_date',
                'value' => $year,
                'compare' => 'LIKE',
            );
        }

        $query = new WP_Query($args);
        $movies = array();

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $movies[] = $this->format_content_item(get_post());
            }
            wp_reset_postdata();
        }

        return rest_ensure_response(array(
            'success' => true,
            'data' => $movies,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => $query->max_num_pages,
                'total_posts' => $query->found_posts,
                'per_page' => $per_page,
            ),
        ));
    }

    /**
     * Get TV shows list
     * @since 1.0.0
     */
    public function get_tvshows($request) {
        $page = $request->get_param('page');
        $per_page = $request->get_param('per_page');

        $args = array(
            'post_type' => 'tvshows',
            'post_status' => 'publish',
            'posts_per_page' => $per_page,
            'paged' => $page,
        );

        $query = new WP_Query($args);
        $tvshows = array();

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $tvshows[] = $this->format_content_item(get_post());
            }
            wp_reset_postdata();
        }

        return rest_ensure_response(array(
            'success' => true,
            'data' => $tvshows,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => $query->max_num_pages,
                'total_posts' => $query->found_posts,
                'per_page' => $per_page,
            ),
        ));
    }

    /**
     * Format content item for API response
     * @since 1.0.0
     */
    private function format_content_item($post) {
        $post_meta = get_post_meta($post->ID);
        
        return array(
            'id' => $post->ID,
            'title' => $post->post_title,
            'slug' => $post->post_name,
            'description' => $post->post_content,
            'excerpt' => $post->post_excerpt,
            'type' => $post->post_type,
            'poster' => dbmovies_get_poster($post->ID, 'medium'),
            'backdrop' => $this->get_backdrop_image($post->ID),
            'rating' => $this->get_rating($post->ID),
            'release_date' => get_post_meta($post->ID, 'release_date', true),
            'runtime' => get_post_meta($post->ID, 'runtime', true),
            'genres' => $this->get_content_genres($post->ID),
            'year' => $this->extract_year_from_date(get_post_meta($post->ID, 'release_date', true)),
            'views' => get_post_meta($post->ID, 'dt_views_count', true) ?: 0,
            'featured' => get_post_meta($post->ID, 'featured', true) === 'yes',
            'permalink' => get_permalink($post->ID),
        );
    }

    /**
     * Get content details
     * @since 1.0.0
     */
    public function get_content_details($request) {
        $id = $request->get_param('id');
        $post = get_post($id);

        if (!$post || !in_array($post->post_type, array('movies', 'tvshows'))) {
            return new WP_Error('not_found', 'Content not found', array('status' => 404));
        }

        $content = $this->format_content_item($post);
        
        // Add additional details
        $content['cast'] = get_post_meta($id, 'cast', true);
        $content['director'] = get_post_meta($id, 'director', true);
        $content['country'] = get_post_meta($id, 'Country', true);
        $content['language'] = get_post_meta($id, 'language', true);
        $content['tagline'] = get_post_meta($id, 'tagline', true);
        $content['imdb_id'] = get_post_meta($id, 'imdb_id', true);
        $content['tmdb_id'] = get_post_meta($id, 'ids', true);

        // For TV shows, add seasons info
        if ($post->post_type === 'tvshows') {
            $content['seasons'] = $this->get_seasons_info($id);
        }

        return rest_ensure_response(array(
            'success' => true,
            'data' => $content,
        ));
    }

    /**
     * Get video sources for content
     * @since 1.0.0
     */
    public function get_video_sources($request) {
        $id = $request->get_param('id');
        $post = get_post($id);

        if (!$post) {
            return new WP_Error('not_found', 'Content not found', array('status' => 404));
        }

        // Use the mobile video helper to get processed sources
        $mobile_sources = DooMobileVideoHelper::get_mobile_sources($id);
        $sources = array();

        foreach ($mobile_sources as $source) {
            $processed = $source['processed'];
            $sources[] = array(
                'id' => $source['index'] + 1,
                'name' => $source['name'],
                'type' => $source['type'],
                'original_url' => $source['original_url'],
                'processed_url' => $processed['url'] ?? $source['original_url'],
                'mobile_compatible' => $source['mobile_compatible'],
                'playable' => $processed['playable'] ?? false,
                'requires_webview' => $processed['requires_webview'] ?? false,
                'mime_type' => $processed['mime_type'] ?? 'video/mp4',
                'quality' => 'HD',
                'language' => 'Original',
            );
        }

        // Add trailer if available
        $trailer = get_post_meta($id, 'youtube_id', true);
        if (!empty($trailer)) {
            $trailer_url = 'https://www.youtube.com/watch?v=' . $trailer;
            $processed_trailer = DooMobileVideoHelper::process_video_url($trailer_url, 'trailer');

            $sources[] = array(
                'id' => 'trailer',
                'name' => 'Trailer',
                'type' => 'trailer',
                'original_url' => $trailer_url,
                'processed_url' => $processed_trailer['url'] ?? $trailer_url,
                'mobile_compatible' => $processed_trailer['playable'] ?? true,
                'playable' => $processed_trailer['playable'] ?? true,
                'video_id' => $processed_trailer['video_id'] ?? $trailer,
                'quality' => 'HD',
                'language' => 'Original',
            );
        }

        return rest_ensure_response(array(
            'success' => true,
            'data' => $sources,
            'mobile_optimized' => true,
        ));
    }

    /**
     * Helper functions
     */
    private function get_backdrop_image($post_id) {
        $backdrop = get_post_meta($post_id, 'backdrop', true);
        if ($backdrop && $backdrop !== 'null') {
            if (substr($backdrop, 0, 1) === '/') {
                return 'https://image.tmdb.org/t/p/w1280' . $backdrop;
            }
            return $backdrop;
        }
        return '';
    }

    private function get_rating($post_id) {
        $rating = get_post_meta($post_id, '_starstruck_avg', true);
        return $rating ? floatval($rating) : 0;
    }

    private function get_content_genres($post_id) {
        $genres = wp_get_post_terms($post_id, 'genres');
        $genre_list = array();
        
        if (!is_wp_error($genres) && !empty($genres)) {
            foreach ($genres as $genre) {
                $genre_list[] = array(
                    'id' => $genre->term_id,
                    'name' => $genre->name,
                    'slug' => $genre->slug,
                );
            }
        }
        
        return $genre_list;
    }

    private function extract_year_from_date($date) {
        if (empty($date)) return '';
        return date('Y', strtotime($date));
    }

    private function get_seasons_info($show_id) {
        $seasons = get_posts(array(
            'post_type' => 'seasons',
            'meta_query' => array(
                array(
                    'key' => 'ids',
                    'value' => $show_id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => -1,
            'orderby' => 'meta_value_num',
            'meta_key' => 'temporada',
            'order' => 'ASC'
        ));

        $seasons_data = array();
        foreach ($seasons as $season) {
            $season_number = get_post_meta($season->ID, 'temporada', true);
            $seasons_data[] = array(
                'id' => $season->ID,
                'season_number' => intval($season_number),
                'title' => $season->post_title,
                'poster' => dbmovies_get_poster($season->ID, 'medium'),
                'episode_count' => $this->get_episode_count($season->ID),
            );
        }

        return $seasons_data;
    }

    private function get_episode_count($season_id) {
        $episodes = get_posts(array(
            'post_type' => 'episodes',
            'meta_query' => array(
                array(
                    'key' => 'season',
                    'value' => $season_id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => -1,
        ));
        return count($episodes);
    }

    /**
     * Search content
     * @since 1.0.0
     */
    public function search_content($request) {
        $query = $request->get_param('q');
        $type = $request->get_param('type');

        $post_types = array('movies', 'tvshows');
        if ($type !== 'all') {
            $post_types = array($type);
        }

        $args = array(
            'post_type' => $post_types,
            'post_status' => 'publish',
            's' => $query,
            'posts_per_page' => 50,
        );

        $search_query = new WP_Query($args);
        $results = array();

        if ($search_query->have_posts()) {
            while ($search_query->have_posts()) {
                $search_query->the_post();
                $results[] = $this->format_content_item(get_post());
            }
            wp_reset_postdata();
        }

        return rest_ensure_response(array(
            'success' => true,
            'data' => $results,
            'total' => $search_query->found_posts,
        ));
    }

    /**
     * Get categories/genres
     * @since 1.0.0
     */
    public function get_categories($request) {
        $genres = get_terms(array(
            'taxonomy' => 'genres',
            'hide_empty' => true,
        ));

        $categories = array();
        if (!is_wp_error($genres)) {
            foreach ($genres as $genre) {
                $categories[] = array(
                    'id' => $genre->term_id,
                    'name' => $genre->name,
                    'slug' => $genre->slug,
                    'count' => $genre->count,
                );
            }
        }

        return rest_ensure_response(array(
            'success' => true,
            'data' => $categories,
        ));
    }

    /**
     * Get featured content
     * @since 1.0.0
     */
    public function get_featured_content($request) {
        // Latest movies
        $latest_movies = new WP_Query(array(
            'post_type' => 'movies',
            'post_status' => 'publish',
            'posts_per_page' => 10,
            'orderby' => 'date',
            'order' => 'DESC',
        ));

        $movies = array();
        if ($latest_movies->have_posts()) {
            while ($latest_movies->have_posts()) {
                $latest_movies->the_post();
                $movies[] = $this->format_content_item(get_post());
            }
            wp_reset_postdata();
        }

        // Latest TV shows
        $latest_tvshows = new WP_Query(array(
            'post_type' => 'tvshows',
            'post_status' => 'publish',
            'posts_per_page' => 10,
            'orderby' => 'date',
            'order' => 'DESC',
        ));

        $tvshows = array();
        if ($latest_tvshows->have_posts()) {
            while ($latest_tvshows->have_posts()) {
                $latest_tvshows->the_post();
                $tvshows[] = $this->format_content_item(get_post());
            }
            wp_reset_postdata();
        }

        // Featured content (if you have a featured meta field)
        $featured = new WP_Query(array(
            'post_type' => array('movies', 'tvshows'),
            'post_status' => 'publish',
            'posts_per_page' => 5,
            'meta_query' => array(
                array(
                    'key' => 'featured',
                    'value' => 'yes',
                    'compare' => '='
                )
            ),
        ));

        $featured_content = array();
        if ($featured->have_posts()) {
            while ($featured->have_posts()) {
                $featured->the_post();
                $featured_content[] = $this->format_content_item(get_post());
            }
            wp_reset_postdata();
        }

        return rest_ensure_response(array(
            'success' => true,
            'data' => array(
                'latest_movies' => $movies,
                'latest_tvshows' => $tvshows,
                'featured' => $featured_content,
            ),
        ));
    }

    /**
     * Get episodes for a TV show
     * @since 1.0.0
     */
    public function get_episodes($request) {
        $show_id = $request->get_param('show_id');
        $season_number = $request->get_param('season');

        // First get the season
        $season = get_posts(array(
            'post_type' => 'seasons',
            'meta_query' => array(
                array(
                    'key' => 'ids',
                    'value' => $show_id,
                    'compare' => '='
                ),
                array(
                    'key' => 'temporada',
                    'value' => $season_number,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1,
        ));

        if (empty($season)) {
            return new WP_Error('not_found', 'Season not found', array('status' => 404));
        }

        $season_id = $season[0]->ID;

        // Get episodes for this season
        $episodes_query = new WP_Query(array(
            'post_type' => 'episodes',
            'meta_query' => array(
                array(
                    'key' => 'season',
                    'value' => $season_id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => -1,
            'orderby' => 'meta_value_num',
            'meta_key' => 'episode',
            'order' => 'ASC'
        ));

        $episodes = array();
        if ($episodes_query->have_posts()) {
            while ($episodes_query->have_posts()) {
                $episodes_query->the_post();
                $episode = $this->format_content_item(get_post());
                $episode['episode_number'] = get_post_meta(get_the_ID(), 'episode', true);
                $episode['season_number'] = $season_number;
                $episodes[] = $episode;
            }
            wp_reset_postdata();
        }

        return rest_ensure_response(array(
            'success' => true,
            'data' => $episodes,
            'season_info' => array(
                'id' => $season_id,
                'season_number' => $season_number,
                'title' => $season[0]->post_title,
            ),
        ));
    }
}

// Initialize the API
new DooMobileAPI();
